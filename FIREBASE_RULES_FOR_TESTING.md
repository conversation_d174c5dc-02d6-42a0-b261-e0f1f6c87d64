# 🔧 قواعد Firebase للاختبار

## 🚨 **قواعد مؤقتة للاختبار - مفتوحة للجميع**

### **Firestore Rules (للاختبار فقط):**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد مؤقتة للاختبار - مفتوحة للجميع
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

### **Storage Rules (للاختبار فقط):**

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد مؤقتة للاختبار - مفتوحة للجميع
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## 📋 **خطوات التطبيق:**

### **الخطوة 1: تحديث قواعد Firestore**
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `legal2025`
3. اذهب إلى **Firestore Database**
4. اضغط على تبويب **Rules**
5. استبدل القواعد الحالية بالقواعد أعلاه
6. اضغط **Publish**

### **الخطوة 2: تحديث قواعد Storage**
1. في نفس Firebase Console
2. اذهب إلى **Storage**
3. اضغط على تبويب **Rules**
4. استبدل القواعد الحالية بالقواعد أعلاه
5. اضغط **Publish**

### **الخطوة 3: التحقق من Authentication**
1. اذهب إلى **Authentication**
2. تبويب **Sign-in method**
3. تأكد من تفعيل:
   - ✅ **Email/Password**
   - ✅ **Anonymous** (اختياري)

## 🔍 **اختبار النظام:**

### **بعد تطبيق القواعد:**
1. شغل التطبيق
2. اذهب لأي مادة
3. اضغط زر الأدمن
4. اختر "🔗 فحص الاتصالات"
5. راجع النتائج

### **إذا كانت النتائج إيجابية:**
1. جرب "إضافة ملف PDF"
2. أدخل:
   - الاسم: "اختبار"
   - الرابط: "https://www.google.com"
3. انتظر رسالة النجاح

## ⚠️ **تحذيرات مهمة:**

### **هذه القواعد للاختبار فقط:**
- تسمح لأي شخص بالقراءة والكتابة
- غير آمنة للإنتاج
- يجب تحديثها قبل النشر

### **للإنتاج، استخدم قواعد آمنة:**
```javascript
// مثال لقواعد آمنة
match /pdfs/{pdfId} {
  allow read: if request.auth != null;
  allow write: if request.auth != null && 
    request.auth.token.email == '<EMAIL>';
}
```

## 🛠️ **حل المشاكل الشائعة:**

### **إذا استمر ظهور "permission-denied":**
1. تأكد من نسخ القواعد بالضبط
2. تأكد من الضغط على **Publish**
3. انتظر دقيقة واحدة للتحديث
4. أعد تشغيل التطبيق

### **إذا استمرت مشاكل الشبكة:**
1. جرب VPN
2. جرب شبكة إنترنت أخرى
3. تحقق من إعدادات Firewall

### **إذا فشل تسجيل الدخول:**
1. تأكد من تفعيل Email/Password في Authentication
2. جرب إنشاء حساب جديد
3. تحقق من تحقق البريد الإلكتروني

## 📞 **للمساعدة:**

### **إذا واجهت مشاكل:**
1. استخدم أداة "🔗 فحص الاتصالات" في التطبيق
2. راجع Console المتصفح للأخطاء
3. تحقق من Firebase Console > Usage للحصص
4. راجع Firebase Console > Logs للأخطاء

### **معلومات المشروع:**
- **Project ID:** legal2025
- **Storage Bucket:** legal2025.firebasestorage.app
- **Admin Email:** <EMAIL>

---

**ملاحظة:** بعد نجاح الاختبار، يجب تحديث القواعد لتكون آمنة للإنتاج!
