import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

/// أداة فحص شاملة لاتصالات Firebase
class FirebaseConnectionChecker {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// فحص شامل لجميع اتصالات Firebase
  static Future<Map<String, dynamic>> checkAllConnections() async {
    final results = <String, dynamic>{};
    
    if (kDebugMode) {
      print('🔍 بدء الفحص الشامل لـ Firebase...');
    }

    // 1. فحص تهيئة Firebase
    results['firebase_initialization'] = await _checkFirebaseInitialization();
    
    // 2. فحص الاتصال بـ Firestore
    results['firestore_connection'] = await _checkFirestoreConnection();
    
    // 3. فحص صلاحيات Firestore
    results['firestore_permissions'] = await _checkFirestorePermissions();
    
    // 4. فحص Firebase Auth
    results['auth_status'] = await _checkAuthStatus();
    
    // 5. فحص Firebase Storage
    results['storage_connection'] = await _checkStorageConnection();
    
    // 6. فحص صلاحيات Storage
    results['storage_permissions'] = await _checkStoragePermissions();
    
    // 7. اختبار عملية كاملة
    results['full_operation_test'] = await _testFullOperation();

    if (kDebugMode) {
      print('✅ انتهى الفحص الشامل');
      _printResults(results);
    }

    return results;
  }

  /// فحص تهيئة Firebase
  static Future<Map<String, dynamic>> _checkFirebaseInitialization() async {
    try {
      final apps = Firebase.apps;
      if (apps.isNotEmpty) {
        final app = Firebase.app();
        return {
          'status': 'success',
          'message': 'Firebase مُهيأ بشكل صحيح',
          'app_name': app.name,
          'project_id': app.options.projectId,
        };
      } else {
        return {
          'status': 'error',
          'message': 'Firebase غير مُهيأ',
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في تهيئة Firebase: $e',
      };
    }
  }

  /// فحص الاتصال بـ Firestore
  static Future<Map<String, dynamic>> _checkFirestoreConnection() async {
    try {
      await _firestore.enableNetwork();
      
      // محاولة قراءة بسيطة
      await _firestore.collection('test').limit(1).get();
      
      return {
        'status': 'success',
        'message': 'الاتصال بـ Firestore يعمل بشكل طبيعي',
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في الاتصال بـ Firestore: $e',
      };
    }
  }

  /// فحص صلاحيات Firestore
  static Future<Map<String, dynamic>> _checkFirestorePermissions() async {
    try {
      // اختبار القراءة
      final readTest = await _firestore.collection('pdfs').limit(1).get();
      
      // اختبار الكتابة
      final testDoc = _firestore.collection('test').doc('permission_test');
      await testDoc.set({
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
      });
      
      // اختبار الحذف
      await testDoc.delete();
      
      return {
        'status': 'success',
        'message': 'صلاحيات Firestore تعمل بشكل كامل',
        'read_count': readTest.docs.length,
      };
    } catch (e) {
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('permission-denied')) {
        return {
          'status': 'error',
          'message': 'مشكلة في صلاحيات Firestore - تحتاج تحديث القواعد',
          'solution': 'تحديث قواعد Firestore في Firebase Console',
        };
      } else {
        return {
          'status': 'error',
          'message': 'خطأ في صلاحيات Firestore: $e',
        };
      }
    }
  }

  /// فحص حالة المصادقة
  static Future<Map<String, dynamic>> _checkAuthStatus() async {
    try {
      final user = _auth.currentUser;
      
      if (user != null) {
        return {
          'status': 'success',
          'message': 'المستخدم مسجل الدخول',
          'user_email': user.email,
          'email_verified': user.emailVerified,
          'is_admin': user.email == '<EMAIL>',
        };
      } else {
        return {
          'status': 'warning',
          'message': 'المستخدم غير مسجل الدخول',
          'solution': 'تسجيل الدخول مطلوب لبعض العمليات',
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص المصادقة: $e',
      };
    }
  }

  /// فحص الاتصال بـ Storage
  static Future<Map<String, dynamic>> _checkStorageConnection() async {
    try {
      final ref = _storage.ref().child('test');
      
      // محاولة الحصول على metadata (لا يحتاج صلاحيات كتابة)
      try {
        await ref.getMetadata();
      } catch (e) {
        // إذا فشل، فهذا طبيعي لأن الملف قد لا يكون موجود
      }
      
      return {
        'status': 'success',
        'message': 'الاتصال بـ Firebase Storage يعمل',
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في الاتصال بـ Storage: $e',
      };
    }
  }

  /// فحص صلاحيات Storage
  static Future<Map<String, dynamic>> _checkStoragePermissions() async {
    try {
      final ref = _storage.ref().child('test/permission_test.txt');
      
      // محاولة رفع ملف تجريبي
      await ref.putString('test permission file');
      
      // محاولة تحميل الملف
      final downloadUrl = await ref.getDownloadURL();
      
      // حذف الملف التجريبي
      await ref.delete();
      
      return {
        'status': 'success',
        'message': 'صلاحيات Storage تعمل بشكل كامل',
        'test_url': downloadUrl,
      };
    } catch (e) {
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('permission-denied') || 
          errorString.contains('unauthorized')) {
        return {
          'status': 'error',
          'message': 'مشكلة في صلاحيات Storage - تحتاج تحديث القواعد',
          'solution': 'تحديث قواعد Storage في Firebase Console',
        };
      } else {
        return {
          'status': 'error',
          'message': 'خطأ في صلاحيات Storage: $e',
        };
      }
    }
  }

  /// اختبار عملية كاملة (إضافة PDF تجريبي)
  static Future<Map<String, dynamic>> _testFullOperation() async {
    try {
      final testId = 'connection_test_${DateTime.now().millisecondsSinceEpoch}';
      
      // إنشاء بيانات PDF تجريبية
      final pdfData = {
        'id': testId,
        'name': 'اختبار الاتصال',
        'url': 'https://www.google.com',
        'category': 'اختبار',
        'subjectId': 'test',
        'subjectName': 'اختبار',
        'yearId': 'test',
        'semesterId': 'test',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'uploadedBy': '<EMAIL>',
        'uploaderName': 'اختبار الاتصال',
        'fileSize': 0.0,
        'fileName': 'connection_test.pdf',
        'fileExtension': 'pdf',
        'isFromUrl': true,
        'originalUrl': 'https://www.google.com',
        'isActive': true,
        'metadata': {'test': true},
      };
      
      // إضافة الوثيقة
      await _firestore.collection('pdfs').doc(testId).set(pdfData);
      
      // التحقق من الإضافة
      final doc = await _firestore.collection('pdfs').doc(testId).get();
      
      if (doc.exists) {
        // حذف الوثيقة التجريبية
        await _firestore.collection('pdfs').doc(testId).delete();
        
        return {
          'status': 'success',
          'message': 'اختبار العملية الكاملة نجح - يمكن إضافة PDF',
          'test_id': testId,
        };
      } else {
        return {
          'status': 'error',
          'message': 'فشل في اختبار العملية الكاملة',
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في اختبار العملية الكاملة: $e',
      };
    }
  }

  /// طباعة النتائج
  static void _printResults(Map<String, dynamic> results) {
    print('\n📊 نتائج فحص Firebase:');
    print('=' * 50);
    
    results.forEach((key, value) {
      final status = value['status'];
      final message = value['message'];
      
      String icon;
      switch (status) {
        case 'success':
          icon = '✅';
          break;
        case 'warning':
          icon = '⚠️';
          break;
        case 'error':
          icon = '❌';
          break;
        default:
          icon = 'ℹ️';
      }
      
      print('$icon $key: $message');
      
      if (value.containsKey('solution')) {
        print('   💡 الحل: ${value['solution']}');
      }
    });
    
    print('=' * 50);
  }

  /// فحص سريع
  static Future<bool> quickCheck() async {
    try {
      // فحص أساسي سريع
      await _firestore.enableNetwork();
      await _firestore.collection('test').limit(1).get();
      
      if (kDebugMode) {
        print('✅ الفحص السريع: Firebase يعمل');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ الفحص السريع: مشكلة في Firebase - $e');
      }
      return false;
    }
  }
}
