import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';
import '../utils/pdf_viewer_tester.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  // حالات العارض
  bool _pdfLoadFailed = false;
  String? _errorMessage;
  bool _showAlternativeViewer = false;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();

    // اختبار سريع للرابط في وضع Debug
    if (kDebugMode) {
      final url = widget.pdfUrl ?? widget.pdfModel?.url;
      if (url != null && url.isNotEmpty) {
        PDFViewerTester.quickTest(url);
      }
    }
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء عارض PDF المناسب
  Widget _buildPdfViewer() {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('\n🔍 ===== PDF Viewer Debug Start =====');
      print('📄 widget.pdfUrl: ${widget.pdfUrl}');
      print('📄 widget.pdfModel?.name: ${widget.pdfModel?.name}');
      print('📄 widget.pdfModel?.url: ${widget.pdfModel?.url}');
      print('📄 Final URL: $pdfUrl');
      print('📄 URL Length: ${pdfUrl?.length ?? 0}');
      print('📄 URL starts with http: ${pdfUrl?.startsWith('http') ?? false}');
      print(
        '📄 URL contains .pdf: ${pdfUrl?.toLowerCase().contains('.pdf') ?? false}',
      );
      print('🔍 ===== PDF Viewer Debug End =====\n');
    }

    // التحقق من وجود رابط
    if (pdfUrl == null || pdfUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF صالح');
      }
      setState(() {
        _isLoading = false;
        _pdfLoadFailed = true;
        _errorMessage = 'لا يوجد رابط للملف';
      });
      return _buildErrorView('لا يوجد رابط للملف');
    }

    // تحويل الرابط إلى رابط مباشر إذا أمكن
    final String processedUrl = _processUrl(pdfUrl);

    if (kDebugMode) {
      print('📄 Processed URL: $processedUrl');
      print('📄 URL Type: ${_getUrlType(processedUrl)}');
    }

    // إذا كان العارض البديل مطلوب
    if (_showAlternativeViewer) {
      return _buildAlternativeViewer(processedUrl);
    }

    // إذا فشل التحميل، عرض العارض البديل
    if (_pdfLoadFailed) {
      return _buildAlternativeViewer(processedUrl);
    }

    // محاولة عرض PDF مباشرة
    return _buildDirectPdfViewer(processedUrl);
  }

  /// معالجة الرابط لتحويله إلى رابط مباشر
  String _processUrl(String url) {
    if (kDebugMode) {
      print('🔄 معالجة الرابط: $url');
    }

    // تحويل روابط Google Drive المختلفة
    if (url.contains('drive.google.com')) {
      String? fileId;

      // النمط الأول: /file/d/FILE_ID/view
      var match = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
      if (match != null) {
        fileId = match.group(1);
      }

      // النمط الثاني: id=FILE_ID في الرابط
      if (fileId == null) {
        match = RegExp(r'[?&]id=([a-zA-Z0-9_-]+)').firstMatch(url);
        if (match != null) {
          fileId = match.group(1);
        }
      }

      if (fileId != null) {
        // إنشاء عدة روابط بديلة
        final directDownload = 'https://drive.google.com/uc?export=download&id=$fileId';
        final viewerUrl = 'https://drive.google.com/file/d/$fileId/preview';
        final embedUrl = 'https://drive.google.com/file/d/$fileId/view';

        if (kDebugMode) {
          print('🔄 تحويل رابط Google Drive:');
          print('   📄 الرابط الأصلي: $url');
          print('   📄 File ID: $fileId');
          print('   📄 رابط التحميل المباشر: $directDownload');
          print('   📄 رابط المعاينة: $viewerUrl');
          print('   📄 رابط العرض: $embedUrl');
        }

        // إرجاع رابط المعاينة أولاً
        return viewerUrl;
      }
    }

    // تحويل روابط Dropbox إلى روابط مباشرة
    if (url.contains('dropbox.com') && url.contains('?dl=0')) {
      final directUrl = url.replaceAll('?dl=0', '?dl=1');
      if (kDebugMode) {
        print('🔄 تحويل رابط Dropbox:');
        print('   📄 الرابط الأصلي: $url');
        print('   📄 الرابط المباشر: $directUrl');
      }
      return directUrl;
    }

    // إرجاع الرابط كما هو إذا لم يحتج تحويل
    if (kDebugMode) {
      print('📄 الرابط لا يحتاج تحويل: $url');
    }
    return url;
  }

  /// تحديد نوع الرابط
  String _getUrlType(String url) {
    if (url.contains('drive.google.com/uc?export=download')) {
      return 'Google Drive Direct';
    } else if (url.contains('drive.google.com')) {
      return 'Google Drive';
    } else if (url.contains('dropbox.com')) {
      return 'Dropbox';
    } else if (url.toLowerCase().endsWith('.pdf')) {
      return 'Direct PDF';
    } else if (url.startsWith('http')) {
      return 'Web URL';
    } else {
      return 'Unknown';
    }
  }

  /// عارض PDF مباشر
  Widget _buildDirectPdfViewer(String url) {
    if (kDebugMode) {
      print('📄 محاولة عرض PDF مباشر: $url');
      print('📄 URL صالح للشبكة: ${url.startsWith('http')}');
    }

    try {
      return SfPdfViewer.network(
        url,
        controller: _pdfViewerController,
        onDocumentLoaded: (PdfDocumentLoadedDetails details) {
          if (kDebugMode) {
            print('✅ تم تحميل PDF بنجاح!');
            print('📄 عدد الصفحات: ${details.document.pages.count}');
            print(
              '📄 حجم الملف: ${details.document.fileStructure.crossReferenceType}',
            );
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
              _totalPages = details.document.pages.count;
              _pdfLoadFailed = false;
              _showAlternativeViewer = false;
            });
          }
        },
        onPageChanged: (PdfPageChangedDetails details) {
          if (mounted) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          }
        },
        onZoomLevelChanged: (PdfZoomDetails details) {
          if (mounted) {
            setState(() {
              _zoomLevel = details.newZoomLevel;
            });
          }
        },
        onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
          if (kDebugMode) {
            print('❌ فشل في تحميل PDF مباشرة!');
            print('❌ نوع الخطأ: ${details.error}');
            print('❌ الرابط: $url');
            print('❌ الوصف: ${details.description}');
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
              _pdfLoadFailed = true;
              _errorMessage =
                  details.description.isNotEmpty
                      ? details.description
                      : 'فشل في تحميل PDF';
              _showAlternativeViewer = true;
            });
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء عارض PDF: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _pdfLoadFailed = true;
          _errorMessage = 'خطأ في إنشاء العارض: $e';
          _showAlternativeViewer = true;
        });
      }

      return _buildAlternativeViewer(url);
    }
  }

  /// عارض بديل للروابط غير المباشرة
  Widget _buildAlternativeViewer(String url) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.picture_as_pdf_rounded,
          size: 80,
          color: Colors.red.shade400,
        ),
        const SizedBox(height: 24),
        Text(
          'عارض PDF',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: [
              Text(
                widget.pdfModel?.name ?? 'ملف PDF',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'اضغط على الزر أدناه لفتح الملف في المتصفح',
                style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        ElevatedButton.icon(
          onPressed: () => _openInBrowser(url),
          icon: const Icon(Icons.open_in_browser, color: Colors.white),
          label: Text(
            'فتح في المتصفح',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade600,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 4,
          ),
        ),
        const SizedBox(height: 16),

        // أزرار العمل
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // زر فتح في المتصفح
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _openInBrowser(url),
                icon: const Icon(Icons.open_in_browser, color: Colors.white),
                label: Text(
                  'فتح في المتصفح',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // زر المحاولة مرة أخرى
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _tryDirectPdfView(url),
                icon: Icon(Icons.refresh, color: Colors.grey[700]),
                label: Text(
                  'محاولة مرة أخرى',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  side: BorderSide(color: Colors.grey.shade400),
                ),
              ),
            ),
          ],
        ),

        // معلومات تشخيص في وضع Debug
        if (kDebugMode) ...[
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات التشخيص (Debug):',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'الرابط: ${url.length > 60 ? '${url.substring(0, 60)}...' : url}',
                  style: GoogleFonts.cairo(fontSize: 10),
                ),
                Text(
                  'طول الرابط: ${url.length}',
                  style: GoogleFonts.cairo(fontSize: 10),
                ),
                Text(
                  'يبدأ بـ http: ${url.startsWith('http')}',
                  style: GoogleFonts.cairo(fontSize: 10),
                ),
                Text(
                  'يحتوي على .pdf: ${url.toLowerCase().contains('.pdf')}',
                  style: GoogleFonts.cairo(fontSize: 10),
                ),
                if (_errorMessage != null)
                  Text(
                    'رسالة الخطأ: $_errorMessage',
                    style: GoogleFonts.cairo(fontSize: 10, color: Colors.red),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// عرض رسالة خطأ
  Widget _buildErrorView(String message) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          message,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// فتح الرابط في المتصفح
  void _openInBrowser(String url) async {
    if (kDebugMode) {
      print('🌐 فتح في المتصفح: $url');
    }

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (kDebugMode) {
          print('❌ لا يمكن فتح الرابط: $url');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الرابط', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فتح الرابط: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// محاولة العرض المباشر مرة أخرى
  void _tryDirectPdfView(String url) {
    if (kDebugMode) {
      print('🔄 محاولة العرض المباشر مرة أخرى: $url');
      print('🔄 إعادة تعيين حالات الخطأ...');
    }

    setState(() {
      _isLoading = true;
      _pdfLoadFailed = false;
      _showAlternativeViewer = false;
      _errorMessage = null;
    });

    // إعادة بناء الواجهة مع العارض المباشر
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // زوم إن
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 1.25;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),

            // زوم أوت
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 0.8;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_out),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1
                      ? () {
                        _pdfViewerController.previousPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages
                      ? () {
                        _pdfViewerController.nextPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            'التكبير: ${(_zoomLevel * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
