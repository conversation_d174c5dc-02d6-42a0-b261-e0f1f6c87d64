import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء عارض PDF المناسب
  Widget _buildPdfViewer() {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('🔍 PDF Viewer Debug:');
      print('📄 pdfUrl: ${widget.pdfUrl}');
      print('📄 pdfModel: ${widget.pdfModel?.name}');
      print('📄 pdfModel.url: ${widget.pdfModel?.url}');
      print('📄 Final URL: $pdfUrl');
    }

    if (pdfUrl == null || pdfUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF');
      }
      return _buildErrorView('لا يوجد رابط للملف');
    }

    // إذا كان الرابط لا يحتوي على PDF، نعرض خيارات أخرى
    if (!pdfUrl.toLowerCase().contains('.pdf') && !_isPdfUrl(pdfUrl)) {
      if (kDebugMode) {
        print('⚠️ الرابط قد لا يكون PDF مباشر: $pdfUrl');
      }
      return _buildAlternativeViewer(pdfUrl);
    }

    // عرض PDF محسن
    return _buildSmartPdfViewer(pdfUrl);
  }

  /// التحقق من أن الرابط يؤدي إلى PDF
  bool _isPdfUrl(String url) {
    return url.toLowerCase().endsWith('.pdf') ||
        url.toLowerCase().contains('pdf') ||
        url.toLowerCase().contains('application/pdf');
  }

  /// عارض PDF محسن مع معالجة ذكية للروابط
  Widget _buildSmartPdfViewer(String url) {
    if (kDebugMode) {
      print('عرض PDF محسن: $url');
    }

    // تحديد رابط العرض المناسب
    final viewUrl = _generateViewUrl(url);

    if (kDebugMode) {
      print('رابط العرض النهائي: $viewUrl');
    }

    return SfPdfViewer.network(
      viewUrl,
      controller: _pdfViewerController,
      onDocumentLoaded: (PdfDocumentLoadedDetails details) {
        if (kDebugMode) {
          print('تم تحميل PDF بنجاح: ${details.document.pages.count} صفحة');
        }
        setState(() {
          _isLoading = false;
          _totalPages = details.document.pages.count;
        });
      },
      onPageChanged: (PdfPageChangedDetails details) {
        setState(() {
          _currentPage = details.newPageNumber;
        });
      },
      onZoomLevelChanged: (PdfZoomDetails details) {
        setState(() {
          _zoomLevel = details.newZoomLevel;
        });
      },
      onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
        if (kDebugMode) {
          print('فشل في تحميل PDF: ${details.error}');
          print('الرابط: $viewUrl');
        }
        setState(() {
          _isLoading = false;
          _errorMessage = 'فشل في تحميل الملف: ${details.error}';
        });
      },
    );
  }

  /// توليد رابط العرض المناسب
  String _generateViewUrl(String originalUrl) {
    if (kDebugMode) {
      print('معالجة الرابط: $originalUrl');
    }

    // إذا كان رابط PDF مباشر
    if (originalUrl.toLowerCase().endsWith('.pdf')) {
      if (kDebugMode) {
        print('رابط PDF مباشر - عرض مباشر');
      }
      return originalUrl;
    }

    // إذا كان رابط Google Drive
    if (originalUrl.contains('drive.google.com')) {
      final fileId = _extractGoogleDriveFileId(originalUrl);
      if (fileId != null) {
        final viewUrl = 'https://drive.google.com/file/d/$fileId/preview';
        if (kDebugMode) {
          print('رابط Google Drive - تحويل إلى: $viewUrl');
        }
        return viewUrl;
      }
    }

    // إذا كان رابط Dropbox
    if (originalUrl.contains('dropbox.com') && originalUrl.contains('?dl=0')) {
      final viewUrl = originalUrl.replaceAll('?dl=0', '?dl=1');
      if (kDebugMode) {
        print('رابط Dropbox - تحويل إلى: $viewUrl');
      }
      return viewUrl;
    }

    // للروابط الأخرى - استخدام Google Docs Viewer
    final encodedUrl = Uri.encodeComponent(originalUrl);
    final viewUrl =
        'https://docs.google.com/viewer?url=$encodedUrl&embedded=true';
    if (kDebugMode) {
      print('رابط عام - استخدام Google Docs Viewer: $viewUrl');
    }
    return viewUrl;
  }

  /// استخراج File ID من رابط Google Drive
  String? _extractGoogleDriveFileId(String url) {
    // النمط الأول: /file/d/FILE_ID/
    var match = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('تم استخراج File ID: $fileId');
      }
      return fileId;
    }

    // النمط الثاني: id=FILE_ID
    match = RegExp(r'[?&]id=([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('تم استخراج File ID: $fileId');
      }
      return fileId;
    }

    if (kDebugMode) {
      print('فشل في استخراج File ID من: $url');
    }
    return null;
  }

  /// عارض بديل للروابط غير المباشرة
  Widget _buildAlternativeViewer(String url) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.picture_as_pdf_rounded,
          size: 80,
          color: Colors.red.shade400,
        ),
        const SizedBox(height: 24),
        Text(
          'عارض PDF',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: [
              Text(
                widget.pdfModel?.name ?? 'ملف PDF',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'اضغط على الزر أدناه لفتح الملف في المتصفح',
                style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        ElevatedButton.icon(
          onPressed: () {
            // إعادة تحميل الملف
            setState(() {
              _isLoading = true;
              _errorMessage = null;
            });
          },
          icon: const Icon(Icons.open_in_browser, color: Colors.white),
          label: Text(
            'فتح في المتصفح',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade600,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 4,
          ),
        ),
        const SizedBox(height: 16),
        TextButton.icon(
          onPressed: () {
            // إعادة تحميل الملف
            setState(() {
              _isLoading = true;
              _errorMessage = null;
            });
          },
          icon: Icon(Icons.refresh, color: Colors.grey[600]),
          label: Text(
            'محاولة العرض المباشر',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
          ),
        ),
      ],
    );
  }

  /// عرض رسالة خطأ
  Widget _buildErrorView(String message) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          message,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // زوم إن
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 1.25;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),

            // زوم أوت
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 0.8;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_out),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1
                      ? () {
                        _pdfViewerController.previousPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages
                      ? () {
                        _pdfViewerController.nextPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            'التكبير: ${(_zoomLevel * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (_errorMessage != null)
            Text(
              'خطأ: $_errorMessage',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red),
            ),
        ],
      ),
    );
  }
}
