import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';
import '../utils/pdf_viewer_tester.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  // حالات العارض
  bool _pdfLoadFailed = false;
  bool _showAlternativeViewer = false;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();

    // اختبار سريع للرابط في وضع Debug
    if (kDebugMode) {
      final url = widget.pdfUrl ?? widget.pdfModel?.url;
      if (url != null && url.isNotEmpty) {
        PDFViewerTester.quickTest(url);
      }
    }
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء عارض PDF المناسب
  Widget _buildPdfViewer() {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('\n🔍 ===== PDF Viewer Debug Start =====');
      print('📄 widget.pdfUrl: ${widget.pdfUrl}');
      print('📄 widget.pdfModel?.name: ${widget.pdfModel?.name}');
      print('📄 widget.pdfModel?.url: ${widget.pdfModel?.url}');
      print('📄 Final URL: $pdfUrl');
      print('📄 URL Length: ${pdfUrl?.length ?? 0}');
      print('📄 URL starts with http: ${pdfUrl?.startsWith('http') ?? false}');
      print(
        '📄 URL contains .pdf: ${pdfUrl?.toLowerCase().contains('.pdf') ?? false}',
      );
      print('🔍 ===== PDF Viewer Debug End =====\n');
    }

    // التحقق من وجود رابط
    if (pdfUrl == null || pdfUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF صالح');
      }
      setState(() {
        _isLoading = false;
        _pdfLoadFailed = true;
      });
      return _buildErrorView('لا يوجد رابط للملف');
    }

    // تحويل الرابط إلى رابط مباشر إذا أمكن
    final String processedUrl = _processUrl(pdfUrl);

    if (kDebugMode) {
      print('📄 Processed URL: $processedUrl');
      print('📄 URL Type: ${_getUrlType(processedUrl)}');
    }

    // إذا كان العارض البديل مطلوب
    if (_showAlternativeViewer) {
      return _buildAlternativeViewer(processedUrl);
    }

    // إذا فشل التحميل، عرض العارض البديل
    if (_pdfLoadFailed) {
      return _buildAlternativeViewer(processedUrl);
    }

    // محاولة عرض PDF مباشرة
    return _buildDirectPdfViewer(processedUrl);
  }

  /// معالجة الرابط لتحويله إلى رابط مباشر
  String _processUrl(String url) {
    if (kDebugMode) {
      print('🔄 معالجة الرابط: $url');
    }

    // تحويل روابط Google Drive المختلفة
    if (url.contains('drive.google.com')) {
      String? fileId;

      // النمط الأول: /file/d/FILE_ID/view
      var match = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
      if (match != null) {
        fileId = match.group(1);
      }

      // النمط الثاني: id=FILE_ID في الرابط
      if (fileId == null) {
        match = RegExp(r'[?&]id=([a-zA-Z0-9_-]+)').firstMatch(url);
        if (match != null) {
          fileId = match.group(1);
        }
      }

      if (fileId != null) {
        // إنشاء عدة روابط بديلة
        final directDownload =
            'https://drive.google.com/uc?export=download&id=$fileId';
        final viewerUrl = 'https://drive.google.com/file/d/$fileId/preview';
        final embedUrl = 'https://drive.google.com/file/d/$fileId/view';

        if (kDebugMode) {
          print('🔄 تحويل رابط Google Drive:');
          print('   📄 الرابط الأصلي: $url');
          print('   📄 File ID: $fileId');
          print('   📄 رابط التحميل المباشر: $directDownload');
          print('   📄 رابط المعاينة: $viewerUrl');
          print('   📄 رابط العرض: $embedUrl');
        }

        // إرجاع رابط المعاينة أولاً
        return viewerUrl;
      }
    }

    // تحويل روابط Dropbox إلى روابط مباشرة
    if (url.contains('dropbox.com') && url.contains('?dl=0')) {
      final directUrl = url.replaceAll('?dl=0', '?dl=1');
      if (kDebugMode) {
        print('🔄 تحويل رابط Dropbox:');
        print('   📄 الرابط الأصلي: $url');
        print('   📄 الرابط المباشر: $directUrl');
      }
      return directUrl;
    }

    // إرجاع الرابط كما هو إذا لم يحتج تحويل
    if (kDebugMode) {
      print('📄 الرابط لا يحتاج تحويل: $url');
    }
    return url;
  }

  /// تحديد نوع الرابط
  String _getUrlType(String url) {
    if (url.contains('drive.google.com/uc?export=download')) {
      return 'Google Drive Direct';
    } else if (url.contains('drive.google.com')) {
      return 'Google Drive';
    } else if (url.contains('dropbox.com')) {
      return 'Dropbox';
    } else if (url.toLowerCase().endsWith('.pdf')) {
      return 'Direct PDF';
    } else if (url.startsWith('http')) {
      return 'Web URL';
    } else {
      return 'Unknown';
    }
  }

  /// عارض PDF مباشر
  Widget _buildDirectPdfViewer(String url) {
    if (kDebugMode) {
      print('📄 محاولة عرض PDF مباشر: $url');
      print('📄 URL صالح للشبكة: ${url.startsWith('http')}');
    }

    try {
      return SfPdfViewer.network(
        url,
        controller: _pdfViewerController,
        onDocumentLoaded: (PdfDocumentLoadedDetails details) {
          if (kDebugMode) {
            print('✅ تم تحميل PDF بنجاح!');
            print('📄 عدد الصفحات: ${details.document.pages.count}');
            print(
              '📄 حجم الملف: ${details.document.fileStructure.crossReferenceType}',
            );
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
              _totalPages = details.document.pages.count;
              _pdfLoadFailed = false;
              _showAlternativeViewer = false;
            });
          }
        },
        onPageChanged: (PdfPageChangedDetails details) {
          if (mounted) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          }
        },
        onZoomLevelChanged: (PdfZoomDetails details) {
          if (mounted) {
            setState(() {
              _zoomLevel = details.newZoomLevel;
            });
          }
        },
        onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
          if (kDebugMode) {
            print('❌ فشل في تحميل PDF مباشرة!');
            print('❌ نوع الخطأ: ${details.error}');
            print('❌ الرابط: $url');
            print('❌ الوصف: ${details.description}');
            print('🔄 سيتم تجربة عارض بديل...');
          }

          // تجربة عارض بديل فوراً
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _tryWebViewPdf(url);
            }
          });
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء عارض PDF: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _pdfLoadFailed = true;
          _showAlternativeViewer = true;
        });
      }

      return _buildAlternativeViewer(url);
    }
  }

  /// عارض بديل متقدم مع خيارات متعددة
  Widget _buildAlternativeViewer(String url) {
    if (kDebugMode) {
      print('🔄 عرض العارض البديل للرابط: $url');
      print('🔄 نوع الرابط: ${_getUrlType(url)}');
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة PDF
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.blue.shade200, width: 2),
            ),
            child: Icon(
              Icons.picture_as_pdf_rounded,
              size: 60,
              color: Colors.blue.shade600,
            ),
          ),
          const SizedBox(height: 24),

          // عنوان
          Text(
            'عارض PDF متقدم',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),

          // معلومات الملف
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              children: [
                Text(
                  widget.pdfModel?.name ?? widget.pdfFileName ?? 'ملف PDF',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade800,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'نوع الرابط: ${_getUrlType(url)}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'اختر طريقة العرض المناسبة:',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // خيارات العرض المتعددة
          _buildMultipleViewOptions(url),
        ],
      ),
    );
  }

  /// بناء خيارات العرض المتعددة
  Widget _buildMultipleViewOptions(String url) {
    final List<Map<String, dynamic>> viewOptions = [];

    // إضافة خيارات حسب نوع الرابط
    if (url.contains('drive.google.com')) {
      final fileId = _extractGoogleDriveFileId(url);
      if (fileId != null) {
        viewOptions.addAll([
          {
            'title': 'عرض مباشر (Google Drive)',
            'subtitle': 'محاولة عرض الملف مباشرة',
            'icon': Icons.visibility,
            'color': Colors.blue,
            'url': 'https://drive.google.com/file/d/$fileId/preview',
          },
          {
            'title': 'تحميل مباشر',
            'subtitle': 'تحميل الملف وعرضه',
            'icon': Icons.download,
            'color': Colors.green,
            'url': 'https://drive.google.com/uc?export=download&id=$fileId',
          },
          {
            'title': 'عارض Google المدمج',
            'subtitle': 'استخدام عارض Google',
            'icon': Icons.open_in_new,
            'color': Colors.orange,
            'url':
                'https://docs.google.com/viewer?url=https://drive.google.com/uc?export=download%26id=$fileId',
          },
        ]);
      }
    } else if (url.toLowerCase().endsWith('.pdf')) {
      viewOptions.add({
        'title': 'عرض PDF مباشر',
        'subtitle': 'محاولة عرض الملف مباشرة',
        'icon': Icons.picture_as_pdf,
        'color': Colors.red,
        'url': url,
      });
    }

    // إضافة خيار المتصفح دائماً
    viewOptions.add({
      'title': 'فتح في المتصفح',
      'subtitle': 'فتح الرابط في المتصفح الخارجي',
      'icon': Icons.open_in_browser,
      'color': Colors.purple,
      'url': url,
      'external': true,
    });

    return Column(
      children:
          viewOptions.map((option) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: ElevatedButton(
                onPressed: () {
                  if (option['external'] == true) {
                    _openInBrowser(option['url']);
                  } else {
                    _tryAlternativeUrl(option['url']);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: option['color'].shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: Row(
                  children: [
                    Icon(option['icon'], size: 24),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            option['title'],
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            option['subtitle'],
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Icon(Icons.arrow_forward_ios, size: 16),
                  ],
                ),
              ),
            );
          }).toList(),
    );
  }

  /// استخراج File ID من رابط Google Drive
  String? _extractGoogleDriveFileId(String url) {
    if (kDebugMode) {
      print('🔍 استخراج File ID من: $url');
    }

    // النمط الأول: /file/d/FILE_ID/
    var match = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('✅ تم العثور على File ID (النمط 1): $fileId');
      }
      return fileId;
    }

    // النمط الثاني: id=FILE_ID
    match = RegExp(r'[?&]id=([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('✅ تم العثور على File ID (النمط 2): $fileId');
      }
      return fileId;
    }

    if (kDebugMode) {
      print('❌ لم يتم العثور على File ID');
    }
    return null;
  }

  /// تجربة رابط بديل
  void _tryAlternativeUrl(String alternativeUrl) {
    if (kDebugMode) {
      print('🔄 تجربة رابط بديل: $alternativeUrl');
    }

    setState(() {
      _isLoading = true;
      _pdfLoadFailed = false;
      _showAlternativeViewer = false;
    });

    // تحديث الرابط وإعادة بناء العارض
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  /// عرض رسالة خطأ
  Widget _buildErrorView(String message) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          message,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// فتح الرابط في المتصفح
  void _openInBrowser(String url) async {
    if (kDebugMode) {
      print('🌐 فتح في المتصفح: $url');
    }

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (kDebugMode) {
          print('❌ لا يمكن فتح الرابط: $url');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الرابط', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فتح الرابط: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تجربة عرض PDF باستخدام WebView
  void _tryWebViewPdf(String url) {
    if (kDebugMode) {
      print('🌐 تجربة عرض PDF باستخدام WebView: $url');
    }

    // إنشاء روابط بديلة للعرض في WebView
    final List<String> webViewUrls = _generateWebViewUrls(url);

    if (kDebugMode) {
      print('🌐 روابط WebView المُنشأة:');
      for (int i = 0; i < webViewUrls.length; i++) {
        print('   ${i + 1}. ${webViewUrls[i]}');
      }
    }

    // عرض نافذة WebView
    _showWebViewDialog(webViewUrls);
  }

  /// إنشاء روابط WebView للعرض
  List<String> _generateWebViewUrls(String originalUrl) {
    final List<String> urls = [];

    if (originalUrl.contains('drive.google.com')) {
      final fileId = _extractGoogleDriveFileId(originalUrl);
      if (fileId != null) {
        // روابط Google Drive للعرض في WebView
        urls.addAll([
          'https://drive.google.com/file/d/$fileId/preview',
          'https://docs.google.com/viewer?url=https://drive.google.com/uc?export=download%26id=$fileId',
          'https://drive.google.com/file/d/$fileId/view',
          originalUrl,
        ]);
      }
    } else {
      // للروابط الأخرى
      if (originalUrl.toLowerCase().endsWith('.pdf')) {
        urls.add(
          'https://docs.google.com/viewer?url=${Uri.encodeComponent(originalUrl)}',
        );
      }
      urls.add(originalUrl);
    }

    return urls;
  }

  /// عرض نافذة WebView
  void _showWebViewDialog(List<String> urls) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            insetPadding: const EdgeInsets.all(10),
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.95,
              height: MediaQuery.of(context).size.height * 0.9,
              child: Column(
                children: [
                  // شريط العنوان
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.picture_as_pdf,
                          color: Colors.white,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            widget.pdfModel?.name ??
                                widget.pdfFileName ??
                                'عارض PDF',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),

                  // محتوى WebView
                  Expanded(child: _buildWebViewContent(urls)),
                ],
              ),
            ),
          ),
    );
  }

  /// بناء محتوى WebView
  Widget _buildWebViewContent(List<String> urls) {
    if (urls.isEmpty) {
      return Center(
        child: Text(
          'لا توجد روابط متاحة للعرض',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'اختر طريقة العرض:',
            style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: urls.length,
              itemBuilder: (context, index) {
                final url = urls[index];
                final title = _getUrlDisplayName(url, index);

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _openInBrowser(url);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getUrlColor(url),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(_getUrlIcon(url), size: 24),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                title,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'فتح في المتصفح',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.white.withValues(alpha: 0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Icon(Icons.open_in_browser, size: 20),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم عرض للرابط
  String _getUrlDisplayName(String url, int index) {
    if (url.contains('docs.google.com/viewer')) {
      return 'عارض Google Docs';
    } else if (url.contains('/preview')) {
      return 'معاينة Google Drive';
    } else if (url.contains('/view')) {
      return 'عرض Google Drive';
    } else if (url.contains('drive.google.com')) {
      return 'Google Drive';
    } else {
      return 'الرابط الأصلي';
    }
  }

  /// الحصول على أيقونة للرابط
  IconData _getUrlIcon(String url) {
    if (url.contains('docs.google.com/viewer')) {
      return Icons.visibility;
    } else if (url.contains('/preview')) {
      return Icons.preview;
    } else if (url.contains('drive.google.com')) {
      return Icons.cloud;
    } else {
      return Icons.link;
    }
  }

  /// الحصول على لون للرابط
  Color _getUrlColor(String url) {
    if (url.contains('docs.google.com/viewer')) {
      return Colors.green.shade600;
    } else if (url.contains('/preview')) {
      return Colors.blue.shade600;
    } else if (url.contains('drive.google.com')) {
      return Colors.orange.shade600;
    } else {
      return Colors.purple.shade600;
    }
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // زوم إن
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 1.25;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),

            // زوم أوت
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 0.8;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_out),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1
                      ? () {
                        _pdfViewerController.previousPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages
                      ? () {
                        _pdfViewerController.nextPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            'التكبير: ${(_zoomLevel * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
