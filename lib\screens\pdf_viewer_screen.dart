import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// نظام العارض الذكي: [رابط PDF] → [تحقق نوعه] → [توليد رابط عرض] → [عرض داخلي]
  Widget _buildPdfViewer() {
    final String? originalUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('\n� ===== نظام العارض الذكي =====');
      print('📄 [المرحلة 1] الرابط الأصلي: $originalUrl');
    }

    // التحقق من وجود رابط
    if (originalUrl == null || originalUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF');
        print('� ===== انتهاء النظام =====\n');
      }
      return _buildErrorView('لا يوجد رابط للملف');
    }

    // المرحلة 2: تحقق من نوع الرابط
    final urlType = _detectUrlType(originalUrl);

    if (kDebugMode) {
      print('🔍 [المرحلة 2] نوع الرابط: $urlType');
    }

    // المرحلة 3: توليد رابط العرض
    final viewUrl = _generateViewUrl(originalUrl, urlType);

    if (kDebugMode) {
      print('🔧 [المرحلة 3] رابط العرض: $viewUrl');
      print('� [المرحلة 4] بدء العرض الداخلي...');
    }

    // المرحلة 4: العرض الداخلي
    return _buildInternalViewer(viewUrl, urlType);
  }

  /// تحديد نوع الرابط
  String _detectUrlType(String url) {
    if (url.contains('drive.google.com')) {
      if (url.contains('/file/d/') || url.contains('id=')) {
        return 'GOOGLE_DRIVE';
      }
      return 'GOOGLE_DRIVE_UNKNOWN';
    }

    if (url.contains('dropbox.com')) {
      return 'DROPBOX';
    }

    if (url.toLowerCase().endsWith('.pdf')) {
      return 'DIRECT_PDF';
    }

    if (url.startsWith('http')) {
      return 'WEB_URL';
    }

    return 'UNKNOWN';
  }

  /// توليد رابط العرض المناسب
  String _generateViewUrl(String originalUrl, String urlType) {
    switch (urlType) {
      case 'GOOGLE_DRIVE':
        return _generateGoogleDriveViewUrl(originalUrl);

      case 'DROPBOX':
        return _generateDropboxViewUrl(originalUrl);

      case 'DIRECT_PDF':
        return originalUrl; // الرابط جاهز للعرض

      default:
        return originalUrl; // محاولة عرض الرابط كما هو
    }
  }

  /// توليد رابط عرض Google Drive
  String _generateGoogleDriveViewUrl(String url) {
    final fileId = _extractGoogleDriveFileId(url);

    if (fileId != null) {
      // الرابط الأقوى للعرض المباشر
      final bestUrl = 'https://drive.google.com/uc?export=download&id=$fileId';

      if (kDebugMode) {
        print('🔧 توليد رابط Google Drive:');
        print('   📄 File ID: $fileId');
        print('   📄 رابط العرض الأمثل: $bestUrl');
      }

      return bestUrl;
    }

    return url; // إرجاع الرابط الأصلي إذا فشل استخراج File ID
  }

  /// توليد رابط عرض Dropbox
  String _generateDropboxViewUrl(String url) {
    if (url.contains('?dl=0')) {
      final directUrl = url.replaceAll('?dl=0', '?dl=1');
      if (kDebugMode) {
        print('🔧 تحويل رابط Dropbox:');
        print('   📄 الأصلي: $url');
        print('   📄 المحول: $directUrl');
      }
      return directUrl;
    }

    return url;
  }

  /// استخراج File ID من رابط Google Drive
  String? _extractGoogleDriveFileId(String url) {
    // النمط الأول: /file/d/FILE_ID/
    var match = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('✅ تم استخراج File ID (النمط 1): $fileId');
      }
      return fileId;
    }

    // النمط الثاني: id=FILE_ID
    match = RegExp(r'[?&]id=([a-zA-Z0-9_-]+)').firstMatch(url);
    if (match != null) {
      final fileId = match.group(1);
      if (kDebugMode) {
        print('✅ تم استخراج File ID (النمط 2): $fileId');
      }
      return fileId;
    }

    if (kDebugMode) {
      print('❌ فشل في استخراج File ID من: $url');
    }
    return null;
  }

  /// العارض الداخلي الموحد
  Widget _buildInternalViewer(String viewUrl, String urlType) {
    if (kDebugMode) {
      print('📱 [العارض الداخلي] بدء العرض:');
      print('   📄 الرابط: $viewUrl');
      print('   📄 النوع: $urlType');
    }

    try {
      return SfPdfViewer.network(
        viewUrl,
        onDocumentLoaded: (PdfDocumentLoadedDetails details) {
          if (kDebugMode) {
            print('✅ [نجح العرض] تم تحميل PDF بنجاح!');
            print('📄 عدد الصفحات: ${details.document.pages.count}');
            print('🚀 ===== انتهاء النظام بنجاح =====\n');
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
              _totalPages = details.document.pages.count;
            });
          }
        },
        onPageChanged: (PdfPageChangedDetails details) {
          if (mounted) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          }
        },
        onZoomLevelChanged: (PdfZoomDetails details) {
          if (mounted) {
            setState(() {
              _zoomLevel = details.newZoomLevel;
            });
          }
        },
        onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
          if (kDebugMode) {
            print('❌ [فشل العرض] فشل في تحميل PDF:');
            print('   📄 نوع الخطأ: ${details.error}');
            print('   📄 الرابط: $viewUrl');
            print('   📄 الوصف: ${details.description}');
            print('🔄 [محاولة بديلة] سيتم تجربة طريقة أخرى...');
          }

          // تجربة طريقة بديلة للعرض
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _tryAlternativeView(viewUrl, urlType);
            }
          });
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ [خطأ في العارض] خطأ في إنشاء العارض: $e');
        print('🔄 [محاولة بديلة] سيتم تجربة طريقة أخرى...');
      }

      return _buildErrorView('فشل في عرض الملف');
    }
  }

  /// تجربة طريقة بديلة للعرض
  void _tryAlternativeView(String failedUrl, String urlType) {
    if (kDebugMode) {
      print('🔄 [الطريقة البديلة] محاولة عرض بديل:');
      print('   📄 الرابط الفاشل: $failedUrl');
      print('   📄 النوع: $urlType');
    }

    if (urlType == 'GOOGLE_DRIVE') {
      // تجربة رابط بديل لـ Google Drive
      final fileId = _extractGoogleDriveFileId(failedUrl);
      if (fileId != null) {
        final alternativeUrl =
            'https://drive.google.com/file/d/$fileId/preview';

        if (kDebugMode) {
          print('🔄 تجربة رابط Google Drive بديل: $alternativeUrl');
        }

        setState(() {
          _isLoading = true;
        });

        // محاولة العرض مع الرابط البديل
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {});
          }
        });
        return;
      }
    }

    // إذا فشلت جميع المحاولات
    if (kDebugMode) {
      print('❌ [فشل نهائي] فشلت جميع محاولات العرض');
      print('🚀 ===== انتهاء النظام بفشل =====\n');
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// عرض رسالة خطأ
  Widget _buildErrorView(String message) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          message,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // زوم إن
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 1.25;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),

            // زوم أوت
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 0.8;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_out),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1
                      ? () {
                        _pdfViewerController.previousPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages
                      ? () {
                        _pdfViewerController.nextPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            'التكبير: ${(_zoomLevel * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
