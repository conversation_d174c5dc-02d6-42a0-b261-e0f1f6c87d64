import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';

/// اختبار مباشر لـ Realtime Database
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔥 بدء اختبار Realtime Database...');
  
  try {
    // تهيئة Firebase
    await Firebase.initializeApp();
    print('✅ تم تهيئة Firebase بنجاح');
    
    // الحصول على مرجع Database
    final database = FirebaseDatabase.instance;
    print('✅ تم الحصول على مرجع Database');
    
    // اختبار الكتابة
    print('🔄 اختبار الكتابة...');
    final testRef = database.ref().child('test_direct');
    await testRef.set({
      'message': 'اختبار مباشر',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'success': true,
    });
    print('✅ تم الكتابة بنجاح');
    
    // اختبار القراءة
    print('🔄 اختبار القراءة...');
    final snapshot = await testRef.get();
    if (snapshot.exists) {
      print('✅ تم القراءة بنجاح: ${snapshot.value}');
    } else {
      print('❌ لم يتم العثور على البيانات');
    }
    
    // اختبار إضافة PDF
    print('🔄 اختبار إضافة PDF...');
    final pdfRef = database.ref().child('pdfs/test_subject/اختبار').push();
    await pdfRef.set({
      'id': pdfRef.key,
      'name': 'اختبار مباشر ${DateTime.now().millisecondsSinceEpoch}',
      'url': 'https://www.google.com',
      'category': 'اختبار',
      'subjectId': 'test_subject',
      'createdAt': DateTime.now().millisecondsSinceEpoch,
      'uploadedBy': '<EMAIL>',
      'isActive': true,
    });
    print('✅ تم إضافة PDF بنجاح: ${pdfRef.key}');
    
    // اختبار قراءة PDFs
    print('🔄 اختبار قراءة PDFs...');
    final pdfsSnapshot = await database.ref().child('pdfs/test_subject/اختبار').get();
    if (pdfsSnapshot.exists) {
      final data = pdfsSnapshot.value as Map<dynamic, dynamic>;
      print('✅ تم العثور على ${data.length} ملف PDF');
      
      // طباعة تفاصيل أول ملف
      final firstPdf = data.values.first;
      print('📄 أول ملف: ${firstPdf['name']}');
    } else {
      print('❌ لم يتم العثور على ملفات PDF');
    }
    
    // تنظيف البيانات التجريبية
    print('🔄 تنظيف البيانات التجريبية...');
    await testRef.remove();
    print('✅ تم تنظيف البيانات');
    
    print('🎉 جميع الاختبارات نجحت! Realtime Database يعمل بشكل مثالي');
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
    
    // تحليل نوع الخطأ
    final errorString = e.toString().toLowerCase();
    if (errorString.contains('permission')) {
      print('💡 الحل: تحديث قواعد Realtime Database في Firebase Console');
      print('   اذهب إلى: Firebase Console > Realtime Database > Rules');
      print('   استبدل بـ: {"rules": {".read": true, ".write": true}}');
    } else if (errorString.contains('network')) {
      print('💡 الحل: تحقق من الاتصال بالإنترنت أو جرب VPN');
    } else if (errorString.contains('firebase')) {
      print('💡 الحل: تحقق من إعدادات Firebase في المشروع');
    }
  }
}

/// اختبار سريع للاتصال فقط
Future<void> quickConnectionTest() async {
  try {
    print('⚡ اختبار سريع للاتصال...');
    
    await Firebase.initializeApp();
    final database = FirebaseDatabase.instance;
    
    // اختبار بسيط
    final ref = database.ref().child('quick_test');
    await ref.set({'test': true});
    final snapshot = await ref.get();
    await ref.remove();
    
    if (snapshot.exists) {
      print('✅ الاختبار السريع نجح - Realtime Database متصل');
    } else {
      print('❌ الاختبار السريع فشل');
    }
    
  } catch (e) {
    print('❌ الاختبار السريع فشل: $e');
  }
}

/// اختبار معلومات Firebase
Future<void> testFirebaseInfo() async {
  try {
    await Firebase.initializeApp();
    final app = Firebase.app();
    
    print('📊 معلومات Firebase:');
    print('   Project ID: ${app.options.projectId}');
    print('   Database URL: ${app.options.databaseURL}');
    print('   App ID: ${app.options.appId}');
    
    if (app.options.databaseURL != null) {
      print('✅ Realtime Database URL موجود');
    } else {
      print('❌ Realtime Database URL غير موجود');
    }
    
  } catch (e) {
    print('❌ خطأ في معلومات Firebase: $e');
  }
}
