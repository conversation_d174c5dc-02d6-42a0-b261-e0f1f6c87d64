import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'services/realtime_pdf_service.dart';

/// اختبار بسيط لـ Realtime Database
class SimpleRealtimeTest {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;

  /// اختبار سريع للاتصال
  static Future<bool> testConnection() async {
    try {
      if (kDebugMode) print('🔥 اختبار الاتصال بـ Realtime Database...');
      
      final ref = _database.ref().child('connection_test');
      await ref.set({
        'test': true,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
      
      final snapshot = await ref.get();
      await ref.remove();
      
      if (snapshot.exists) {
        if (kDebugMode) print('✅ الاتصال يعمل بشكل طبيعي');
        return true;
      } else {
        if (kDebugMode) print('❌ مشكلة في الاتصال');
        return false;
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في الاتصال: $e');
      return false;
    }
  }

  /// اختبار إضافة PDF
  static Future<bool> testAddPDF() async {
    try {
      if (kDebugMode) print('🔥 اختبار إضافة PDF...');
      
      final testName = 'اختبار_${DateTime.now().millisecondsSinceEpoch}';
      
      final success = await RealtimePDFService.addPDF(
        name: testName,
        url: 'https://www.google.com',
        category: 'اختبار',
        subjectId: 'test_subject',
        adminEmail: '<EMAIL>',
      );
      
      if (success) {
        if (kDebugMode) print('✅ تم إضافة PDF بنجاح: $testName');
        return true;
      } else {
        if (kDebugMode) print('❌ فشل في إضافة PDF');
        return false;
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة PDF: $e');
      return false;
    }
  }

  /// اختبار قراءة PDFs
  static Future<bool> testReadPDFs() async {
    try {
      if (kDebugMode) print('🔥 اختبار قراءة PDFs...');
      
      final pdfs = await RealtimePDFService.getPDFs(
        subjectId: 'test_subject',
        category: 'اختبار',
      );
      
      if (kDebugMode) print('✅ تم العثور على ${pdfs.length} ملف PDF');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في قراءة PDFs: $e');
      return false;
    }
  }

  /// اختبار شامل
  static Future<Map<String, bool>> runAllTests() async {
    if (kDebugMode) print('🚀 بدء الاختبار الشامل...');
    
    final results = <String, bool>{};
    
    // اختبار الاتصال
    results['connection'] = await testConnection();
    
    // اختبار إضافة PDF
    results['add_pdf'] = await testAddPDF();
    
    // اختبار قراءة PDFs
    results['read_pdfs'] = await testReadPDFs();
    
    // حساب النتائج
    final successCount = results.values.where((v) => v).length;
    final totalTests = results.length;
    
    if (kDebugMode) {
      print('📊 نتائج الاختبار:');
      results.forEach((test, success) {
        final icon = success ? '✅' : '❌';
        print('   $icon $test: ${success ? 'نجح' : 'فشل'}');
      });
      print('📈 معدل النجاح: $successCount/$totalTests (${(successCount/totalTests*100).round()}%)');
    }
    
    return results;
  }

  /// فحص معلومات Firebase
  static Future<Map<String, String>> getFirebaseInfo() async {
    try {
      final app = _database.app;
      return {
        'project_id': app.options.projectId ?? 'غير محدد',
        'database_url': app.options.databaseURL ?? 'غير محدد',
        'app_id': app.options.appId ?? 'غير محدد',
        'status': 'متصل',
      };
    } catch (e) {
      return {
        'status': 'خطأ: $e',
      };
    }
  }

  /// تنظيف البيانات التجريبية
  static Future<bool> cleanupTestData() async {
    try {
      if (kDebugMode) print('🧹 تنظيف البيانات التجريبية...');
      
      // حذف بيانات الاختبار
      await _database.ref().child('test_subject/اختبار').remove();
      await _database.ref().child('connection_test').remove();
      
      if (kDebugMode) print('✅ تم تنظيف البيانات التجريبية');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تنظيف البيانات: $e');
      return false;
    }
  }
}
