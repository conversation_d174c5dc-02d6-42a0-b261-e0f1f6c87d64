import 'package:flutter/foundation.dart';

/// أداة اختبار شاملة لعارض PDF
class PDFViewerTester {
  
  /// اختبار شامل لعارض PDF
  static Future<Map<String, dynamic>> runComprehensiveTest() async {
    final results = <String, dynamic>{};
    
    if (kDebugMode) {
      print('\n🧪 ===== بدء اختبار عارض PDF الشامل =====');
    }
    
    // اختبار 1: روابط PDF مباشرة
    results['direct_pdf_urls'] = await _testDirectPDFUrls();
    
    // اختبار 2: روابط غير مباشرة
    results['indirect_urls'] = await _testIndirectUrls();
    
    // اختبار 3: روابط غير صالحة
    results['invalid_urls'] = await _testInvalidUrls();
    
    // اختبار 4: روابط فارغة
    results['empty_urls'] = await _testEmptyUrls();
    
    if (kDebugMode) {
      print('🧪 ===== انتهاء اختبار عارض PDF الشامل =====\n');
      _printResults(results);
    }
    
    return results;
  }
  
  /// اختبار روابط PDF مباشرة
  static Future<Map<String, dynamic>> _testDirectPDFUrls() async {
    final testUrls = [
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
      'https://www.clickdimensions.com/links/TestPDFfile.pdf',
    ];
    
    final results = <String, dynamic>{
      'test_name': 'روابط PDF مباشرة',
      'total_urls': testUrls.length,
      'results': <Map<String, dynamic>>[],
    };
    
    for (final url in testUrls) {
      final urlResult = await _testSingleUrl(url, 'direct_pdf');
      results['results'].add(urlResult);
    }
    
    return results;
  }
  
  /// اختبار روابط غير مباشرة
  static Future<Map<String, dynamic>> _testIndirectUrls() async {
    final testUrls = [
      'https://www.google.com',
      'https://www.github.com',
      'https://www.stackoverflow.com',
    ];
    
    final results = <String, dynamic>{
      'test_name': 'روابط غير مباشرة',
      'total_urls': testUrls.length,
      'results': <Map<String, dynamic>>[],
    };
    
    for (final url in testUrls) {
      final urlResult = await _testSingleUrl(url, 'indirect');
      results['results'].add(urlResult);
    }
    
    return results;
  }
  
  /// اختبار روابط غير صالحة
  static Future<Map<String, dynamic>> _testInvalidUrls() async {
    final testUrls = [
      'invalid-url',
      'http://non-existent-domain-12345.com',
      'https://httpstat.us/404',
    ];
    
    final results = <String, dynamic>{
      'test_name': 'روابط غير صالحة',
      'total_urls': testUrls.length,
      'results': <Map<String, dynamic>>[],
    };
    
    for (final url in testUrls) {
      final urlResult = await _testSingleUrl(url, 'invalid');
      results['results'].add(urlResult);
    }
    
    return results;
  }
  
  /// اختبار روابط فارغة
  static Future<Map<String, dynamic>> _testEmptyUrls() async {
    final testUrls = ['', '   ', null];
    
    final results = <String, dynamic>{
      'test_name': 'روابط فارغة',
      'total_urls': testUrls.length,
      'results': <Map<String, dynamic>>[],
    };
    
    for (final url in testUrls) {
      final urlResult = await _testSingleUrl(url, 'empty');
      results['results'].add(urlResult);
    }
    
    return results;
  }
  
  /// اختبار رابط واحد
  static Future<Map<String, dynamic>> _testSingleUrl(String? url, String type) async {
    final result = <String, dynamic>{
      'url': url ?? 'null',
      'type': type,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    try {
      // تحليل الرابط
      result['is_null'] = url == null;
      result['is_empty'] = url?.isEmpty ?? true;
      result['length'] = url?.length ?? 0;
      result['starts_with_http'] = url?.startsWith('http') ?? false;
      result['contains_pdf'] = url?.toLowerCase().contains('.pdf') ?? false;
      result['is_valid_format'] = _isValidUrlFormat(url);
      
      // تحديد النتيجة المتوقعة
      if (url == null || url.isEmpty) {
        result['expected_behavior'] = 'error_view';
        result['should_show_alternative'] = false;
      } else if (url.toLowerCase().contains('.pdf') && url.startsWith('http')) {
        result['expected_behavior'] = 'direct_pdf_viewer';
        result['should_show_alternative'] = false;
      } else if (url.startsWith('http')) {
        result['expected_behavior'] = 'alternative_viewer';
        result['should_show_alternative'] = true;
      } else {
        result['expected_behavior'] = 'error_or_alternative';
        result['should_show_alternative'] = true;
      }
      
      result['test_passed'] = true;
      
    } catch (e) {
      result['test_passed'] = false;
      result['error'] = e.toString();
    }
    
    if (kDebugMode) {
      print('🔍 اختبار رابط: ${url ?? "null"}');
      print('   📊 النوع: $type');
      print('   📊 السلوك المتوقع: ${result['expected_behavior']}');
      print('   📊 النتيجة: ${result['test_passed'] ? "نجح" : "فشل"}');
    }
    
    return result;
  }
  
  /// التحقق من صحة تنسيق الرابط
  static bool _isValidUrlFormat(String? url) {
    if (url == null || url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  /// طباعة النتائج
  static void _printResults(Map<String, dynamic> results) {
    print('\n📊 ===== نتائج اختبار عارض PDF =====');
    
    results.forEach((testCategory, categoryResults) {
      if (categoryResults is Map<String, dynamic>) {
        print('\n📋 ${categoryResults['test_name']}:');
        print('   📊 إجمالي الاختبارات: ${categoryResults['total_urls']}');
        
        final testResults = categoryResults['results'] as List<Map<String, dynamic>>;
        int passedTests = 0;
        
        for (final testResult in testResults) {
          final passed = testResult['test_passed'] as bool;
          if (passed) passedTests++;
          
          final icon = passed ? '✅' : '❌';
          print('   $icon ${testResult['url']}: ${testResult['expected_behavior']}');
        }
        
        print('   📈 معدل النجاح: $passedTests/${testResults.length}');
      }
    });
    
    print('\n📊 ===== انتهاء النتائج =====\n');
  }
  
  /// اختبار سريع لرابط واحد
  static void quickTest(String url) {
    if (kDebugMode) {
      print('\n⚡ اختبار سريع للرابط: $url');
      print('📄 طول الرابط: ${url.length}');
      print('📄 يبدأ بـ http: ${url.startsWith('http')}');
      print('📄 يحتوي على .pdf: ${url.toLowerCase().contains('.pdf')}');
      print('📄 تنسيق صالح: ${_isValidUrlFormat(url)}');
      
      if (url.isEmpty) {
        print('🎯 النتيجة المتوقعة: عرض رسالة خطأ');
      } else if (url.toLowerCase().contains('.pdf') && url.startsWith('http')) {
        print('🎯 النتيجة المتوقعة: عرض PDF مباشر');
      } else if (url.startsWith('http')) {
        print('🎯 النتيجة المتوقعة: عرض العارض البديل');
      } else {
        print('🎯 النتيجة المتوقعة: خطأ أو عارض بديل');
      }
      print('⚡ انتهاء الاختبار السريع\n');
    }
  }
}
