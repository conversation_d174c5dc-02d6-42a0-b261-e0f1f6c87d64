import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

/// أداة تشخيص مشاكل PDF
class PDFDiagnostic {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// تشخيص شامل لنظام PDF
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final results = <String, dynamic>{};
    
    if (kDebugMode) {
      print('🔧 بدء التشخيص الشامل لنظام PDF...');
    }

    // 1. فحص الاتصال بـ Firebase
    results['firebase_connection'] = await _checkFirebaseConnection();
    
    // 2. فحص المصادقة
    results['authentication'] = await _checkAuthentication();
    
    // 3. فحص صلاحيات Firestore
    results['firestore_permissions'] = await _checkFirestorePermissions();
    
    // 4. فحص Firebase Storage
    results['storage_permissions'] = await _checkStoragePermissions();
    
    // 5. فحص بيانات PDF الموجودة
    results['existing_pdfs'] = await _checkExistingPDFs();
    
    // 6. اختبار إضافة PDF تجريبي
    results['test_pdf_creation'] = await _testPDFCreation();

    if (kDebugMode) {
      print('✅ انتهى التشخيص الشامل');
      _printDiagnosticResults(results);
    }

    return results;
  }

  /// فحص الاتصال بـ Firebase
  static Future<Map<String, dynamic>> _checkFirebaseConnection() async {
    try {
      await _firestore.enableNetwork();
      return {
        'status': 'success',
        'message': 'الاتصال بـ Firebase يعمل بشكل طبيعي',
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في الاتصال بـ Firebase: $e',
      };
    }
  }

  /// فحص المصادقة
  static Future<Map<String, dynamic>> _checkAuthentication() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        return {
          'status': 'success',
          'message': 'المستخدم مسجل الدخول',
          'user_email': user.email,
          'user_verified': user.emailVerified,
        };
      } else {
        return {
          'status': 'warning',
          'message': 'المستخدم غير مسجل الدخول',
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص المصادقة: $e',
      };
    }
  }

  /// فحص صلاحيات Firestore
  static Future<Map<String, dynamic>> _checkFirestorePermissions() async {
    try {
      // محاولة قراءة مجموعة PDFs
      final querySnapshot = await _firestore
          .collection('pdfs')
          .limit(1)
          .get();
      
      // محاولة كتابة وثيقة تجريبية
      final testDoc = _firestore.collection('test').doc('diagnostic');
      await testDoc.set({
        'test': true,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // حذف الوثيقة التجريبية
      await testDoc.delete();
      
      return {
        'status': 'success',
        'message': 'صلاحيات Firestore تعمل بشكل طبيعي',
        'existing_pdfs_count': querySnapshot.docs.length,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في صلاحيات Firestore: $e',
      };
    }
  }

  /// فحص صلاحيات Firebase Storage
  static Future<Map<String, dynamic>> _checkStoragePermissions() async {
    try {
      final ref = _storage.ref().child('test/diagnostic.txt');
      
      // محاولة رفع ملف تجريبي
      await ref.putString('test diagnostic file');
      
      // محاولة تحميل الملف
      final downloadUrl = await ref.getDownloadURL();
      
      // حذف الملف التجريبي
      await ref.delete();
      
      return {
        'status': 'success',
        'message': 'صلاحيات Storage تعمل بشكل طبيعي',
        'test_url': downloadUrl,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في صلاحيات Storage: $e',
      };
    }
  }

  /// فحص بيانات PDF الموجودة
  static Future<Map<String, dynamic>> _checkExistingPDFs() async {
    try {
      final querySnapshot = await _firestore
          .collection('pdfs')
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();
      
      final pdfs = querySnapshot.docs.map((doc) => {
        'id': doc.id,
        'name': doc.data()['name'] ?? 'غير محدد',
        'category': doc.data()['category'] ?? 'غير محدد',
        'createdAt': doc.data()['createdAt'] ?? 'غير محدد',
      }).toList();
      
      return {
        'status': 'success',
        'message': 'تم العثور على ${querySnapshot.docs.length} ملف PDF',
        'pdfs': pdfs,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص ملفات PDF: $e',
      };
    }
  }

  /// اختبار إضافة PDF تجريبي
  static Future<Map<String, dynamic>> _testPDFCreation() async {
    try {
      final testId = 'diagnostic_${DateTime.now().millisecondsSinceEpoch}';
      final testData = {
        'id': testId,
        'name': 'ملف تجريبي للتشخيص',
        'url': 'https://www.google.com',
        'category': 'اختبار',
        'subjectId': 'test',
        'subjectName': 'اختبار',
        'yearId': 'test',
        'semesterId': 'test',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'uploadedBy': '<EMAIL>',
        'uploaderName': 'أداة التشخيص',
        'fileSize': 0.0,
        'fileName': 'diagnostic_test.pdf',
        'fileExtension': 'pdf',
        'isFromUrl': true,
        'originalUrl': 'https://www.google.com',
        'isActive': true,
        'metadata': {'diagnostic': true},
      };
      
      // إضافة الوثيقة
      await _firestore
          .collection('pdfs')
          .doc(testId)
          .set(testData);
      
      // التحقق من الإضافة
      final doc = await _firestore
          .collection('pdfs')
          .doc(testId)
          .get();
      
      if (doc.exists) {
        // حذف الوثيقة التجريبية
        await _firestore
            .collection('pdfs')
            .doc(testId)
            .delete();
        
        return {
          'status': 'success',
          'message': 'اختبار إضافة PDF نجح بشكل كامل',
          'test_id': testId,
        };
      } else {
        return {
          'status': 'error',
          'message': 'فشل في إضافة PDF التجريبي',
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في اختبار إضافة PDF: $e',
      };
    }
  }

  /// طباعة نتائج التشخيص
  static void _printDiagnosticResults(Map<String, dynamic> results) {
    print('\n📊 نتائج التشخيص:');
    print('=' * 50);
    
    results.forEach((key, value) {
      final status = value['status'];
      final message = value['message'];
      
      String icon;
      switch (status) {
        case 'success':
          icon = '✅';
          break;
        case 'warning':
          icon = '⚠️';
          break;
        case 'error':
          icon = '❌';
          break;
        default:
          icon = 'ℹ️';
      }
      
      print('$icon $key: $message');
    });
    
    print('=' * 50);
  }

  /// تشخيص سريع
  static Future<bool> quickDiagnostic() async {
    try {
      // فحص الاتصال
      await _firestore.enableNetwork();
      
      // فحص الصلاحيات
      await _firestore.collection('test').doc('quick').set({'test': true});
      await _firestore.collection('test').doc('quick').delete();
      
      if (kDebugMode) {
        print('✅ التشخيص السريع: كل شيء يعمل بشكل طبيعي');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ التشخيص السريع: مشكلة في النظام - $e');
      }
      return false;
    }
  }
}
